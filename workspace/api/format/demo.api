syntax = "v1"

type User {
	Id          int64  `json:"id"`
	Name        string `json:"name"`
	Age         int    `json:"age"`
	Description string `json:"description"`
}

type Student {
	Id          int64  `json:"id"`
	No          int64  `json:"no"`
	Name        string `json:"name"`
	Age         int    `json:"age"`
	Description string `json:"description"`
}

service User {
	@handler ping
	get /ping
}

