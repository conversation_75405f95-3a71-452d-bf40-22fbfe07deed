// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.5
// Source: demo.proto

package server

import (
	"context"

	"go_zero_demo/workspace/rpc/demo/demo"
	"go_zero_demo/workspace/rpc/demo/internal/logic"
	"go_zero_demo/workspace/rpc/demo/internal/svc"
)

type DemoServer struct {
	svcCtx *svc.ServiceContext
	demo.UnimplementedDemoServer
}

func NewDemoServer(svcCtx *svc.ServiceContext) *DemoServer {
	return &DemoServer{
		svcCtx: svcCtx,
	}
}

func (s *DemoServer) Ping(ctx context.Context, in *demo.Request) (*demo.Response, error) {
	l := logic.NewPingLogic(ctx, s.svcCtx)
	return l.Ping(in)
}
