package main

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/zeromicro/go-zero/core/stores/mon"
)

type User struct {
	ID       primitive.ObjectID `bson:"_id,omitempty" json:"id,omitempty"`
	Username string             `bson:"username,omitempty" json:"username,omitempty"`
	Password string             `bson:"password,omitempty" json:"password,omitempty"`
	UpdateAt time.Time          `bson:"updateAt,omitempty" json:"updateAt,omitempty"`
	CreateAt time.Time          `bson:"createAt,omitempty" json:"createAt,omitempty"`
}

func main() {
	conn := mon.MustNewModel("********************************", "db", "collection")
	ctx := context.Background()
	u := &User{
		// 不设置 ID，让 MongoDB 自动生成
		Username: "username",
		Password: "password",
		UpdateAt: time.Now(),
		CreateAt: time.Now(),
	}
	// insert one
	result, err := conn.InsertOne(ctx, u)
	if err != nil {
		panic(err)
	}

	// 获取插入后的 ObjectID
	insertedID := result.InsertedID.(primitive.ObjectID)
	fmt.Printf("插入成功，生成的 ObjectID: %s\n", insertedID.Hex())

	// 查询 - 使用实际插入的 ID
	var newUser User
	err = conn.FindOne(ctx, &newUser, bson.M{"_id": insertedID})
	if err != nil {
		panic(err)
	}
	fmt.Printf("查询成功，用户名: %s\n", newUser.Username)

	// 更新
	newUser.Username = "newUsername"
	newUser.UpdateAt = time.Now()
	_, err = conn.ReplaceOne(ctx, bson.M{"_id": newUser.ID}, newUser)
	if err != nil {
		panic(err)
	}
	fmt.Printf("更新成功，新用户名: %s\n", newUser.Username)

	// 删除
	_, err = conn.DeleteOne(ctx, bson.M{"_id": newUser.ID})
	if err != nil {
		panic(err)
	}
	fmt.Println("删除成功")
}
